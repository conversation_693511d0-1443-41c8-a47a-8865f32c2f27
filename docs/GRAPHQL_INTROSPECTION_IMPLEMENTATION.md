# GraphQL Introspection Implementation

## Overview

This document describes the implementation of GraphQL introspection support for the xbit-agent admin API, allowing tools like Postman to automatically discover and load the GraphQL schema without requiring authentication.

## Problem

Previously, the admin GraphQL endpoint (`/api/dex-agent/admin/graphql`) required API key authentication for all requests, including introspection queries. This prevented tools like Postman from automatically loading the schema when pasting the GraphQL endpoint URL.

## Solution

Implemented selective authentication bypass for GraphQL introspection queries while maintaining security for all other operations.

## Implementation Details

### 1. Enhanced API Key Middleware

**File**: `internal/controller/graphql/middlewares/api_key.go`

**Changes**:
- Added `isIntrospectionQuery()` function to detect introspection queries
- Modified `ApiKeyAuth()` middleware to:
  - Read and parse request body to identify introspection queries
  - Allow introspection queries to bypass API key authentication
  - Set `isIntrospection` context flag for downstream handlers
  - Maintain API key requirement for all other queries

**Key Features**:
- Detects common introspection patterns: `__schema`, `__type`, `IntrospectionQuery`
- Preserves request body for downstream handlers
- Maintains backward compatibility

### 2. Updated GraphQL Directives

**Files**: 
- `internal/controller/admin/graphql/directive.go`
- `internal/controller/graphql/directive.go`

**Changes**:
- Modified `AdminAuthDirective()` to allow introspection queries
- Modified `AuthDirective()` to allow introspection queries
- Added introspection context check before authentication validation

### 3. Enhanced JWT Middleware

**File**: `internal/controller/graphql/middlewares/jwt.go`

**Changes**:
- Added introspection query detection
- Skip JWT processing for introspection queries
- Maintain existing JWT validation for regular queries

## Security Considerations

### What's Protected
- All admin GraphQL operations still require valid API key
- User GraphQL operations still require valid JWT tokens
- Only introspection queries bypass authentication

### What's Exposed
- GraphQL schema structure (types, fields, directives)
- Field descriptions and deprecation information
- No actual data or business logic

### Risk Assessment
- **Low Risk**: Schema introspection is a standard GraphQL feature
- **No Data Exposure**: Only metadata about schema structure is revealed
- **Maintained Security**: All actual operations require proper authentication

## Usage

### For Developers
1. **Postman Integration**:
   - Paste GraphQL endpoint URL: `http://localhost:8080/api/dex-agent/admin/graphql`
   - Postman will automatically load schema without requiring API key
   - Add API key header for actual queries

2. **GraphQL Playground**:
   - Access playground at: `http://localhost:8080/api/dex-agent/admin/graphql/playground`
   - Schema will load automatically
   - Add API key for executing queries

### For Tools
Any GraphQL client tool can now:
- Discover schema automatically
- Provide autocomplete and validation
- Generate documentation from schema

## Testing

### Introspection Query (No Auth Required)
```bash
curl -X POST \
  http://localhost:8080/api/dex-agent/admin/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { types { name } } }"}'
```

### Regular Query (Auth Required)
```bash
curl -X POST \
  http://localhost:8080/api/dex-agent/admin/graphql \
  -H "Content-Type: application/json" \
  -H "x-api-key: YOUR_API_KEY" \
  -d '{"query": "query { adminGetAllTasks { id name } }"}'
```

## Best Practices

### For Production
1. **Monitor Introspection Usage**: Consider logging introspection queries
2. **Rate Limiting**: Apply rate limiting to prevent abuse
3. **Schema Optimization**: Keep schema descriptions informative but not revealing sensitive implementation details

### For Development
1. **Use Introspection**: Leverage automatic schema discovery in development tools
2. **Schema Documentation**: Maintain good field descriptions for better developer experience
3. **Testing**: Regularly test both introspection and authenticated queries

## Configuration

No additional configuration is required. The feature is enabled by default and works with existing API key authentication setup.

## Compatibility

- **Backward Compatible**: Existing API key authentication continues to work
- **Tool Compatible**: Works with Postman, GraphQL Playground, Apollo Studio, etc.
- **Standard Compliant**: Follows GraphQL introspection specification

## Future Considerations

1. **Conditional Introspection**: Consider adding configuration to disable introspection in production if needed
2. **Enhanced Logging**: Add specific logging for introspection queries
3. **Performance Optimization**: Cache introspection results if needed for high-traffic scenarios
