package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// CompleteTask is the resolver for the completeTask field.
func (r *mutationResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	panic(fmt.Errorf("not implemented: CompleteTask - completeTask"))
}

// ClaimTaskReward is the resolver for the claimTaskReward field.
func (r *mutationResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskClaimResponse, error) {
	panic(fmt.Errorf("not implemented: ClaimTaskReward - claimTaskReward"))
}

// Claim<PERSON>ashback is the resolver for the claimCashback field.
func (r *mutationResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	panic(fmt.Errorf("not implemented: ClaimCashback - claimCashback"))
}

// RefreshTaskList is the resolver for the refreshTaskList field.
func (r *mutationResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	panic(fmt.Errorf("not implemented: RefreshTaskList - refreshTaskList"))
}

// ActivityCashbackDashboard is the resolver for the activityCashbackDashboard field.
func (r *queryResolver) ActivityCashbackDashboard(ctx context.Context) (*gql_model.UserDashboardResponse, error) {
	panic(fmt.Errorf("not implemented: ActivityCashbackDashboard - activityCashbackDashboard"))
}

// TaskCenter is the resolver for the taskCenter field.
func (r *queryResolver) TaskCenter(ctx context.Context) (*gql_model.TaskCenterResponse, error) {
	panic(fmt.Errorf("not implemented: TaskCenter - taskCenter"))
}

// TierBenefits is the resolver for the tierBenefits field.
func (r *queryResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	panic(fmt.Errorf("not implemented: TierBenefits - tierBenefits"))
}

// UserTaskProgress is the resolver for the userTaskProgress field.
func (r *queryResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	panic(fmt.Errorf("not implemented: UserTaskProgress - userTaskProgress"))
}

// TaskCompletionHistory is the resolver for the taskCompletionHistory field.
func (r *queryResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	panic(fmt.Errorf("not implemented: TaskCompletionHistory - taskCompletionHistory"))
}

// UserTierInfo is the resolver for the userTierInfo field.
func (r *queryResolver) UserTierInfo(ctx context.Context) (*gql_model.UserTierInfo, error) {
	panic(fmt.Errorf("not implemented: UserTierInfo - userTierInfo"))
}

// TaskCategories is the resolver for the taskCategories field.
func (r *queryResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	panic(fmt.Errorf("not implemented: TaskCategories - taskCategories"))
}

// TasksByCategory is the resolver for the tasksByCategory field.
func (r *queryResolver) TasksByCategory(ctx context.Context, categoryName string) ([]*gql_model.ActivityTask, error) {
	panic(fmt.Errorf("not implemented: TasksByCategory - tasksByCategory"))
}
