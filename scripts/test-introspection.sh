#!/bin/bash

# Test GraphQL Introspection Implementation
# This script tests that introspection queries work without authentication
# while regular queries still require API key

set -e

# Configuration
ADMIN_ENDPOINT="http://localhost:8080/api/dex-agent/admin/graphql"
USER_ENDPOINT="http://localhost:8080/api/dex-agent/graphql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing GraphQL Introspection Implementation${NC}"
echo "=============================================="

# Test 1: Admin Introspection Query (should work without API key)
echo -e "\n${YELLOW}Test 1: Admin Introspection Query (no API key)${NC}"
RESPONSE=$(curl -s -X POST \
  "$ADMIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { types { name } } }"}')

if echo "$RESPONSE" | grep -q '"types"'; then
    echo -e "${GREEN}✓ PASS: Admin introspection works without API key${NC}"
else
    echo -e "${RED}✗ FAIL: Admin introspection failed${NC}"
    echo "Response: $RESPONSE"
fi

# Test 2: User Introspection Query (should work without JWT)
echo -e "\n${YELLOW}Test 2: User Introspection Query (no JWT)${NC}"
RESPONSE=$(curl -s -X POST \
  "$USER_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { types { name } } }"}')

if echo "$RESPONSE" | grep -q '"types"'; then
    echo -e "${GREEN}✓ PASS: User introspection works without JWT${NC}"
else
    echo -e "${RED}✗ FAIL: User introspection failed${NC}"
    echo "Response: $RESPONSE"
fi

# Test 3: Admin Regular Query (should fail without API key)
echo -e "\n${YELLOW}Test 3: Admin Regular Query (no API key - should fail)${NC}"
RESPONSE=$(curl -s -X POST \
  "$ADMIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { adminGetAllTasks { id } }"}')

if echo "$RESPONSE" | grep -q "unauthorized\|invalid.*API key"; then
    echo -e "${GREEN}✓ PASS: Admin regular query properly requires API key${NC}"
else
    echo -e "${RED}✗ FAIL: Admin regular query should require API key${NC}"
    echo "Response: $RESPONSE"
fi

# Test 4: Type Introspection Query
echo -e "\n${YELLOW}Test 4: Type Introspection Query${NC}"
RESPONSE=$(curl -s -X POST \
  "$ADMIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __type(name: \"Query\") { name fields { name } } }"}')

if echo "$RESPONSE" | grep -q '"name".*"Query"'; then
    echo -e "${GREEN}✓ PASS: Type introspection works${NC}"
else
    echo -e "${RED}✗ FAIL: Type introspection failed${NC}"
    echo "Response: $RESPONSE"
fi

# Test 5: Complex Introspection Query
echo -e "\n${YELLOW}Test 5: Complex Introspection Query${NC}"
RESPONSE=$(curl -s -X POST \
  "$ADMIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query IntrospectionQuery { __schema { queryType { name } mutationType { name } types { kind name description } directives { name description locations } } }"
  }')

if echo "$RESPONSE" | grep -q '"queryType".*"mutationType"'; then
    echo -e "${GREEN}✓ PASS: Complex introspection works${NC}"
else
    echo -e "${RED}✗ FAIL: Complex introspection failed${NC}"
    echo "Response: $RESPONSE"
fi

# Test 6: Check for adminAuth directive
echo -e "\n${YELLOW}Test 6: Check for adminAuth directive${NC}"
RESPONSE=$(curl -s -X POST \
  "$ADMIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { directives { name } } }"}')

if echo "$RESPONSE" | grep -q 'adminAuth'; then
    echo -e "${GREEN}✓ PASS: adminAuth directive found in schema${NC}"
else
    echo -e "${YELLOW}⚠ WARNING: adminAuth directive not found (may be expected)${NC}"
fi

echo -e "\n${YELLOW}=============================================="
echo -e "Introspection Test Complete${NC}"

# Instructions for manual testing
echo -e "\n${YELLOW}Manual Testing Instructions:${NC}"
echo "1. Open Postman"
echo "2. Create new GraphQL request"
echo "3. Paste URL: $ADMIN_ENDPOINT"
echo "4. Postman should automatically load the schema"
echo "5. Try autocomplete in the query editor"
echo ""
echo "For authenticated queries, add header:"
echo "x-api-key: YOUR_API_KEY_HERE"
