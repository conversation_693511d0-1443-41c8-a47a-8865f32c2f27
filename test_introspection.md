# Testing GraphQL Introspection

## Admin GraphQL Endpoint
URL: `http://localhost:8080/api/dex-agent/admin/graphql`

## Test Introspection Query

### 1. Basic Schema Query
```graphql
query IntrospectionQuery {
  __schema {
    queryType {
      name
    }
    mutationType {
      name
    }
    subscriptionType {
      name
    }
    types {
      ...FullType
    }
    directives {
      name
      description
      locations
      args {
        ...InputValue
      }
    }
  }
}

fragment FullType on __Type {
  kind
  name
  description
  fields(includeDeprecated: true) {
    name
    description
    args {
      ...InputValue
    }
    type {
      ...TypeRef
    }
    isDeprecated
    deprecationReason
  }
  inputFields {
    ...InputValue
  }
  interfaces {
    ...TypeRef
  }
  enumValues(includeDeprecated: true) {
    name
    description
    isDeprecated
    deprecationReason
  }
  possibleTypes {
    ...TypeRef
  }
}

fragment InputValue on __InputValue {
  name
  description
  type {
    ...TypeRef
  }
  defaultValue
}

fragment TypeRef on __Type {
  kind
  name
  ofType {
    kind
    name
    ofType {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
              }
            }
          }
        }
      }
    }
  }
}
```

### 2. Simple Schema Query
```graphql
query {
  __schema {
    types {
      name
    }
  }
}
```

### 3. Query Type Information
```graphql
query {
  __type(name: "Query") {
    name
    fields {
      name
      type {
        name
      }
    }
  }
}
```

## Testing Steps

1. **Without API Key** (should work for introspection):
   ```bash
   curl -X POST \
     http://localhost:8080/api/dex-agent/admin/graphql \
     -H "Content-Type: application/json" \
     -d '{"query": "query { __schema { types { name } } }"}'
   ```

2. **With API Key** (should work for all queries):
   ```bash
   curl -X POST \
     http://localhost:8080/api/dex-agent/admin/graphql \
     -H "Content-Type: application/json" \
     -H "x-api-key: YOUR_API_KEY" \
     -d '{"query": "query { __schema { types { name } } }"}'
   ```

3. **Regular Query without API Key** (should fail):
   ```bash
   curl -X POST \
     http://localhost:8080/api/dex-agent/admin/graphql \
     -H "Content-Type: application/json" \
     -d '{"query": "query { adminGetAllTasks { id name } }"}'
   ```

## Expected Results

- Introspection queries should work without API key
- Regular admin queries should require API key
- Schema should be discoverable in tools like Postman, GraphQL Playground, etc.
